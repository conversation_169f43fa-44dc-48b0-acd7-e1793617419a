# Bug Fix: Lỗi khi khóa tài khoản người dùng

## Vấn đề
Khi thực hiện chức năng khóa/mở khóa tài khoản người dùng, hệ thống gặp lỗi mặc dù chỉ cập nhật trường `blocked`.

## Nguyên nhân
Vấn đề chính nằm ở file `src/utils/base/lifecycles.js`:

1. **Lifecycle hook lỗi**: Mỗi khi có thao tác update user, lifecycle hook `baseAfterUpdate` được gọi
2. **Content type không tồn tại**: Hook này cố gắng tạo audit log với content type `'api::audit-log.audit-log'` nhưng content type này không tồn tại trong hệ thống
3. **Lỗi cascade**: Việc tạo audit log thất bại khiến toàn bộ thao tác update user bị lỗi

## Giải pháp đã thực hiện

### 1. Tạo Content Type Audit Log
- Tạo content type `audit-log` tại `src/api/audit-log/`
- <PERSON><PERSON><PERSON> bao gồm các trường: `contentType`, `action`, `result`, `author`, `params`

### 2. Cải thiện Lifecycle Hooks
- Thêm try-catch để tránh lỗi cascade
- Thêm async/await để xử lý đúng
- Thêm logging để debug

### 3. Cải thiện User Service
- Thêm validation cho parameters
- Kiểm tra user tồn tại trước khi update
- Trả về response có cấu trúc rõ ràng
- Thêm logging chi tiết

### 4. Cải thiện Controller
- Thêm validation cho request body
- Xử lý error tốt hơn
- Trả về response có cấu trúc

### 5. Cải thiện Frontend
- Thêm validation parameters
- Xử lý response và error message tốt hơn
- Hiển thị thông báo chi tiết hơn

## Files đã thay đổi

1. `src/utils/base/lifecycles.js` - Fix lifecycle hooks
2. `src/api/audit-log/` - Tạo mới content type
3. `src/plugins/management/server/services/user-service.js` - Cải thiện service
4. `src/plugins/management/server/controllers/user-controller.js` - Cải thiện controller
5. `src/plugins/management/admin/src/components/UserList.js` - Cải thiện frontend

## Kiểm tra sau khi fix

1. Restart Strapi server để load content type mới
2. Thử khóa/mở khóa tài khoản
3. Kiểm tra console logs
4. Kiểm tra database có tạo audit logs không

## Lưu ý
- Audit logs sẽ được tạo cho tất cả thao tác create/update/delete
- Có thể tắt audit logging bằng cách comment code trong lifecycles.js nếu không cần thiết
- Content type audit-log có thể được customize thêm theo nhu cầu
